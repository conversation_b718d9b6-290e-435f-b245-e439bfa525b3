<!-- Generated by SpecStory -->

# Untitled (2025-05-28 00:28:39)

_**User**_

I'd like to get these ppt files downloaded @https://www.10xebitda.com/investment-banking-presentations/ 

Can you get them?

---

_**Assistant**_

I'll help you download the PowerPoint presentations from the 10xebitda.com investment banking presentations page. Let me start by exploring the current workspace and then create a script to scrape and download these files.

---

<details>
            <summary>Listed current directory • **8** results</summary>
        
| Name |
|-------|
| 📁 `.git` |
| 📄 `README.md` |
| 📄 `.python-version` |
| 📄 `.gitignore` |
| 📄 `main.py` |
| 📄 `pyproject.toml` |
| 📁 `.specstory` |
| 📄 `.cursorindexingignore` |

</details>

---

Let me check the existing files to understand the current setup:

---

Read file: pyproject.toml

---

Read file: main.py

---

Now I'll create a comprehensive script to scrape and download all the PowerPoint presentations from the 10xebitda.com website. Based on the search results, I can see there are many presentations from various investment banks.

---



---



---



---



---



---

Now I'll implement the solution step by step:

---

```diff
  name = "apollo-ppt-corpus"
  version = "0.1.0"
- description = "Add your description here"
+ description = "Download investment banking presentations from 10xebitda.com"
  readme = "README.md"
  requires-python = ">=3.13"
- dependencies = []
+ dependencies = [
+     "requests>=2.31.0",
+     "beautifulsoup4>=4.12.0",
+     "aiohttp>=3.9.0",
+     "aiofiles>=23.2.0",
+     "loguru>=0.7.0",
+     "python-dotenv>=1.0.0",
+     "lxml>=4.9.0",
+ ]
  
```

---

Cannot edit files blocked by globalIgnore

---



---


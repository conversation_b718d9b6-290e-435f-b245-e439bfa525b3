import os
import asyncio
import aiohttp
import aiofiles
from pathlib import Path
from typing import List
from urllib.parse import urlparse
from loguru import logger as log
from config import CONFIG
from scraper import Presentation

class PresentationDownloader:
    """Async downloader for presentation files."""
    
    def __init__(self):
        self.download_dir = Path(CONFIG.DOWNLOAD_DIR)
        self.semaphore = asyncio.Semaphore(CONFIG.MAX_CONCURRENT_DOWNLOADS)
        
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility."""
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Limit length and clean up
        filename = filename.strip()[:200]
        return filename
    
    def _get_file_path(self, presentation: Presentation) -> Path:
        """Generate file path for presentation."""
        # Create bank directory
        bank_dir = self.download_dir / self._sanitize_filename(presentation.bank)
        bank_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate filename
        base_name = self._sanitize_filename(presentation.title)
        if presentation.date:
            base_name = f"{presentation.date}_{base_name}"
        
        # Add extension if not present
        if not base_name.lower().endswith(f'.{presentation.file_extension}'):
            base_name += f'.{presentation.file_extension}'
        
        return bank_dir / base_name
    
    async def _download_file(self, session: aiohttp.ClientSession, presentation: Presentation) -> bool:
        """Download a single presentation file."""
        async with self.semaphore:
            try:
                file_path = self._get_file_path(presentation)
                
                # Skip if file already exists
                if file_path.exists():
                    log.info(f"File already exists, skipping: {file_path.name}")
                    return True
                
                log.info(f"Downloading: {presentation.title}")
                
                async with session.get(presentation.url) as response:
                    if response.status == 200:
                        async with aiofiles.open(file_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                        
                        file_size = file_path.stat().st_size
                        log.success(f"Downloaded: {file_path.name} ({file_size:,} bytes)")
                        return True
                    else:
                        log.error(f"Failed to download {presentation.title}: HTTP {response.status}")
                        return False
                        
            except Exception as e:
                log.error(f"Error downloading {presentation.title}: {e}")
                return False
            
            finally:
                # Rate limiting
                await asyncio.sleep(CONFIG.REQUEST_DELAY)
    
    async def download_presentations(self, presentations: List[Presentation]) -> None:
        """Download all presentations asynchronously."""
        if not presentations:
            log.warning("No presentations to download")
            return
        
        # Create download directory
        self.download_dir.mkdir(exist_ok=True)
        log.info(f"Download directory: {self.download_dir.absolute()}")
        
        # Setup HTTP session
        timeout = aiohttp.ClientTimeout(total=CONFIG.TIMEOUT)
        headers = {
            'User-Agent': CONFIG.USER_AGENT,
            'Accept': 'application/pdf,application/vnd.ms-powerpoint,*/*',
        }
        
        async with aiohttp.ClientSession(timeout=timeout, headers=headers) as session:
            # Create download tasks
            tasks = [
                self._download_file(session, presentation) 
                for presentation in presentations
            ]
            
            log.info(f"Starting download of {len(tasks)} presentations...")
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Count results
            successful = sum(1 for r in results if r is True)
            failed = len(results) - successful
            
            log.success(f"Download complete: {successful} successful, {failed} failed")
    
    def get_download_summary(self) -> dict:
        """Get summary of downloaded files."""
        if not self.download_dir.exists():
            return {"total_files": 0, "total_size": 0, "banks": {}}
        
        summary = {"total_files": 0, "total_size": 0, "banks": {}}
        
        for bank_dir in self.download_dir.iterdir():
            if bank_dir.is_dir():
                bank_files = list(bank_dir.glob("*"))
                bank_size = sum(f.stat().st_size for f in bank_files if f.is_file())
                
                summary["banks"][bank_dir.name] = {
                    "files": len(bank_files),
                    "size": bank_size
                }
                summary["total_files"] += len(bank_files)
                summary["total_size"] += bank_size
        
        return summary 
import os
import sys
from dataclasses import dataclass
from typing import Optional
from loguru import logger as log
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class Config:
    """Configuration for the Apollo PPT Corpus scraper."""
    
    # URLs and paths
    BASE_URL: str = os.getenv("BASE_URL", "https://www.10xebitda.com/investment-banking-presentations/")
    DOWNLOAD_DIR: str = os.getenv("DOWNLOAD_DIR", "downloads")
    
    # Download settings
    MAX_CONCURRENT_DOWNLOADS: int = int(os.getenv("MAX_CONCURRENT_DOWNLOADS", "5"))
    REQUEST_DELAY: float = float(os.getenv("REQUEST_DELAY", "1.0"))
    
    # HTTP settings
    USER_AGENT: str = os.getenv("USER_AGENT", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")
    TIMEOUT: int = 30
    
    # Logging
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FORMAT: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
    
    def log_init(self):
        """Initialize loguru with color formatting."""
        log.remove()  # Remove default handler
        log.add(sys.stderr, format=self.LOG_FORMAT, level=self.LOG_LEVEL)
        log.info("Logging initialized")

# Global config instance
CONFIG = Config() 
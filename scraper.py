import re
import time
from dataclasses import dataclass
from typing import List, Dict, Optional
from urllib.parse import urljoin, urlparse
import requests
from bs4 import BeautifulSoup
from loguru import logger as log
from config import CONFIG

@dataclass
class Presentation:
    """Data class for presentation information."""
    title: str
    url: str
    bank: str
    company: str
    date: Optional[str] = None
    file_extension: str = "pdf"

class PresentationScraper:
    """Scraper for 10xebitda investment banking presentations."""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': CONFIG.USER_AGENT,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
    
    def _get_page_content(self, url: str) -> Optional[BeautifulSoup]:
        """Fetch and parse page content."""
        try:
            log.info(f"Fetching page: {url}")
            response = self.session.get(url, timeout=CONFIG.TIMEOUT)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            log.success(f"Successfully parsed page: {url}")
            return soup
            
        except requests.RequestException as e:
            log.error(f"Failed to fetch {url}: {e}")
            return None
    
    def _extract_bank_name(self, section_text: str) -> str:
        """Extract bank name from section headers."""
        # Common bank name patterns
        bank_patterns = {
            'bofa': 'BofA Securities',
            'bank of america': 'BofA Securities',
            'barclays': 'Barclays',
            'centerview': 'Centerview Partners',
            'citi': 'Citi',
            'credit suisse': 'Credit Suisse',
            'goldman': 'Goldman Sachs',
            'jp morgan': 'JP Morgan',
            'jpmorgan': 'JP Morgan',
            'lazard': 'Lazard',
            'moelis': 'Moelis',
            'morgan stanley': 'Morgan Stanley',
            'perella': 'Perella Weinberg Partners',
            'pjt': 'PJT Partners',
            'svb': 'SVB Securities',
            'leerink': 'Leerink Partners',
            'qatalyst': 'Qatalyst Partners'
        }
        
        text_lower = section_text.lower()
        for pattern, bank_name in bank_patterns.items():
            if pattern in text_lower:
                return bank_name
        
        return "Unknown"
    
    def _extract_company_name(self, title: str) -> str:
        """Extract company name from presentation title."""
        # Remove common prefixes and clean up
        title = re.sub(r'^(sale of|acquisition of|merger of|buyout of)', '', title, flags=re.IGNORECASE)
        title = re.sub(r'\s+', ' ', title).strip()
        
        # Extract first company name (before "to", "by", "with")
        match = re.search(r'^([^(]+?)(?:\s+(?:to|by|with|and)\s+|\s*\()', title, re.IGNORECASE)
        if match:
            return match.group(1).strip()
        
        return title[:50]  # Fallback to first 50 chars
    
    def _extract_date(self, text: str) -> Optional[str]:
        """Extract date from presentation text."""
        # Look for date patterns like (17-Jan-2024), (27-Apr-2023), etc.
        date_match = re.search(r'\((\d{1,2}-\w{3}-\d{4})\)', text)
        if date_match:
            return date_match.group(1)
        
        # Look for other date patterns
        date_match = re.search(r'\((\w{3}-\d{4})\)', text)
        if date_match:
            return date_match.group(1)
        
        return None
    
    def scrape_presentations(self) -> List[Presentation]:
        """Scrape all presentation links from the main page."""
        soup = self._get_page_content(CONFIG.BASE_URL)
        if not soup:
            return []
        
        presentations = []
        current_bank = "Unknown"
        
        # Find all sections and links
        for element in soup.find_all(['h3', 'h4', 'li', 'a']):
            # Update current bank when we hit a bank section header
            if element.name in ['h3', 'h4']:
                bank_name = self._extract_bank_name(element.get_text())
                if bank_name != "Unknown":
                    current_bank = bank_name
                    log.info(f"Found bank section: {current_bank}")
            
            # Look for presentation links
            elif element.name == 'a' and element.get('href'):
                href = element.get('href')
                text = element.get_text().strip()
                
                # Check if this looks like a presentation link
                if any(ext in href.lower() for ext in ['.pdf', '.ppt', '.pptx']) or \
                   any(keyword in text.lower() for keyword in ['presentation', 'pitch', 'fairness']):
                    
                    # Build full URL
                    full_url = urljoin(CONFIG.BASE_URL, href)
                    
                    # Extract file extension
                    parsed_url = urlparse(href)
                    file_ext = parsed_url.path.split('.')[-1].lower() if '.' in parsed_url.path else 'pdf'
                    
                    # Extract company and date
                    company = self._extract_company_name(text)
                    date = self._extract_date(text)
                    
                    presentation = Presentation(
                        title=text,
                        url=full_url,
                        bank=current_bank,
                        company=company,
                        date=date,
                        file_extension=file_ext
                    )
                    
                    presentations.append(presentation)
                    log.info(f"Found presentation: {text} ({current_bank})")
        
        log.success(f"Found {len(presentations)} presentations total")
        return presentations
    
    def close(self):
        """Close the session."""
        self.session.close() 